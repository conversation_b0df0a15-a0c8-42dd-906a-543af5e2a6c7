"""Gemini LLM provider implementation."""

import asyncio
import io
import base64
from typing import Dict, Any, Optional, List
from PIL import Image
import google.generativeai as genai
from google.generativeai import types
from google.generativeai.types import <PERSON>rmCategory, HarmBlockThreshold

from .base import <PERSON>MProvider, LLMResponse, ImageInput


class GeminiProvider(LLMProvider):
    """Gemini LLM provider for image and text processing."""
    
    def __init__(self, model_name: str = "gemini-2.0-flash-exp", api_key: str = "", **kwargs):
        """
        Initialize the Gemini provider.
        
        Args:
            model_name: Gemini model name
            api_key: Google API key
            **kwargs: Additional configuration
        """
        super().__init__(model_name, api_key, **kwargs)
        
        # Configure the Gemini API
        genai.configure(api_key=self.api_key)
        
        # Initialize the model
        self.model = genai.GenerativeModel(
            model_name=self.model_name,
            generation_config=self._get_generation_config(),
            safety_settings=self._get_safety_settings(),
            config=types.GenerateContentConfig(thinking_config=types.ThinkingConfig(thinking_budget=0))  
        )
        
        self.logger.info(f"Initialized Gemini provider with model: {self.model_name}")
    
    def _get_generation_config(self) -> Dict[str, Any]:
        """Get generation configuration for the model."""
        return {
            "temperature": self.config.get("temperature", 0.1),
            "top_p": self.config.get("top_p", 0.8),
            "top_k": self.config.get("top_k", 40),
            "max_output_tokens": self.config.get("max_output_tokens", 8192),
        }
    
    def _get_safety_settings(self) -> Dict[HarmCategory, HarmBlockThreshold]:
        """Get safety settings for the model."""
        return {
            HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        }
    
    def _image_to_parts(self, image: Image.Image) -> List[Dict[str, Any]]:
        """
        Convert PIL Image to Gemini-compatible parts.
        
        Args:
            image: PIL Image object
            
        Returns:
            List of parts for Gemini API
        """
        # Convert image to bytes
        buffer = io.BytesIO()
        
        # Ensure image is in RGB mode
        if image.mode != "RGB":
            image = image.convert("RGB")
        
        # Save as JPEG for better compression
        image.save(buffer, format="JPEG", quality=85)
        image_bytes = buffer.getvalue()
        
        return [{
            "mime_type": "image/jpeg",
            "data": image_bytes
        }]
    
    async def process_image(self, input_data: ImageInput) -> LLMResponse:
        """
        Process an image with a text prompt using Gemini.
        
        Args:
            input_data: ImageInput containing image and prompt
            
        Returns:
            LLMResponse with the processed result
        """
        try:
            self.logger.info("Processing image with Gemini")
            
            # Preprocess the image
            processed_image = self.preprocess_image(input_data.image)
            
            # Convert image to Gemini format
            image_parts = self._image_to_parts(processed_image)
            
            # Prepare the content for the model
            content = [input_data.prompt] + image_parts
            
            # Generate response
            response = await asyncio.to_thread(
                self.model.generate_content,
                content,
                stream=False
            )
            
            # Extract the text response
            if response.text:
                self.logger.info("Successfully processed image with Gemini")
                return LLMResponse(
                    content=response.text,
                    metadata={
                        "model": self.model_name,
                        "prompt_tokens": getattr(response.usage_metadata, 'prompt_token_count', 0) if hasattr(response, 'usage_metadata') else 0,
                        "completion_tokens": getattr(response.usage_metadata, 'candidates_token_count', 0) if hasattr(response, 'usage_metadata') else 0,
                        "image_size": processed_image.size,
                        "original_image_size": input_data.image.size
                    },
                    success=True
                )
            else:
                error_msg = "No text response from Gemini"
                self.logger.error(error_msg)
                return LLMResponse(
                    content="",
                    metadata={"model": self.model_name},
                    success=False,
                    error_message=error_msg
                )
                
        except Exception as e:
            error_msg = f"Error processing image with Gemini: {str(e)}"
            self.logger.error(error_msg)
            return LLMResponse(
                content="",
                metadata={"model": self.model_name},
                success=False,
                error_message=error_msg
            )
    
    async def process_text(self, prompt: str, context: Optional[str] = None) -> LLMResponse:
        """
        Process text with a prompt using Gemini.
        
        Args:
            prompt: The prompt to process
            context: Optional context for the prompt
            
        Returns:
            LLMResponse with the processed result
        """
        try:
            self.logger.info("Processing text with Gemini")
            
            # Combine context and prompt if context is provided
            full_prompt = prompt
            if context:
                full_prompt = f"Context:\n{context}\n\nPrompt:\n{prompt}"
            
            # Generate response
            response = await asyncio.to_thread(
                self.model.generate_content,
                full_prompt,
                stream=False
            )
            
            # Extract the text response
            if response.text:
                self.logger.info("Successfully processed text with Gemini")
                return LLMResponse(
                    content=response.text,
                    metadata={
                        "model": self.model_name,
                        "prompt_tokens": getattr(response.usage_metadata, 'prompt_token_count', 0) if hasattr(response, 'usage_metadata') else 0,
                        "completion_tokens": getattr(response.usage_metadata, 'candidates_token_count', 0) if hasattr(response, 'usage_metadata') else 0,
                    },
                    success=True
                )
            else:
                error_msg = "No text response from Gemini"
                self.logger.error(error_msg)
                return LLMResponse(
                    content="",
                    metadata={"model": self.model_name},
                    success=False,
                    error_message=error_msg
                )
                
        except Exception as e:
            error_msg = f"Error processing text with Gemini: {str(e)}"
            self.logger.error(error_msg)
            return LLMResponse(
                content="",
                metadata={"model": self.model_name},
                success=False,
                error_message=error_msg
            )
    
    def validate_config(self) -> bool:
        """
        Validate the Gemini provider configuration.
        
        Returns:
            True if configuration is valid, False otherwise
        """
        try:
            if not self.api_key:
                self.logger.error("Gemini API key is required")
                return False
            
            # Test the API key by making a simple request
            test_model = genai.GenerativeModel(self.model_name)
            response = test_model.generate_content("Hello")
            
            if response.text:
                self.logger.info("Gemini configuration is valid")
                return True
            else:
                self.logger.error("Failed to get response from Gemini")
                return False
                
        except Exception as e:
            self.logger.error(f"Gemini configuration validation failed: {e}")
            return False
    
    def get_supported_image_formats(self) -> List[str]:
        """Get list of supported image formats for Gemini."""
        return ["PNG", "JPEG", "JPG", "WEBP", "HEIC", "HEIF"]
    
    def get_max_image_size(self) -> tuple:
        """Get maximum supported image size for Gemini."""
        return (3072, 3072)  # Gemini's recommended maximum
