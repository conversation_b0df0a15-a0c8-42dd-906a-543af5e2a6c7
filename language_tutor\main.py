"""Main application controller for Language Tutor."""

import asyncio
import logging
import signal
import sys
from pathlib import Path
from typing import Optional

from .config.settings import settings
from .capture.screen_capture import ScreenCapture
from .input.hotkey_listener import HotkeyListener
from .llm.gemini_provider import GeminiProvider
from .llm.base import LLMProviderFactory
from .pipeline.ocr_pipeline import OCRPipeline
from .pipeline.analysis_pipeline import AnalysisPipeline
from .utils.logging_config import setup_logging

logger = logging.getLogger(__name__)


class LanguageTutorApp:
    """Main application class that orchestrates all components."""

    def __init__(self):
        """Initialize the Language Tutor application."""
        self.screen_capture: Optional[ScreenCapture] = None
        self.hotkey_listener: Optional[HotkeyListener] = None
        self.llm_provider: Optional[GeminiProvider] = None
        self.ocr_pipeline: Optional[OCRPipeline] = None
        self.analysis_pipeline: Optional[AnalysisPipeline] = None
        self.is_running = False
        self.event_loop: Optional[asyncio.AbstractEventLoop] = None

        # Setup logging
        setup_logging(settings.log_level)
        logger.info("Language Tutor application initializing...")
    
    async def initialize(self) -> bool:
        """
        Initialize all application components.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            logger.info("Initializing application components...")
            
            # Initialize screen capture
            self.screen_capture = ScreenCapture(save_path=settings.screenshot_save_path)
            logger.info("Screen capture initialized")
            
            # Initialize LLM provider
            self.llm_provider = GeminiProvider(
                model_name=settings.gemini_model,
                api_key=settings.gemini_api_key,
                max_retries=settings.max_retries,
                timeout_seconds=settings.timeout_seconds
            )
            
            # Validate LLM configuration
            if not self.llm_provider.validate_config():
                logger.error("LLM provider configuration validation failed")
                return False
            
            logger.info("LLM provider initialized and validated")
            
            # Register the provider in the factory
            LLMProviderFactory.register_provider("gemini", GeminiProvider)
            
            # Initialize processing pipelines
            self.ocr_pipeline = OCRPipeline(self.llm_provider)
            self.analysis_pipeline = AnalysisPipeline(self.llm_provider)
            logger.info("Processing pipelines initialized")
            
            # Initialize hotkey listener
            self.hotkey_listener = HotkeyListener(settings.hotkey_combination)
            self.hotkey_listener.set_callback(self._on_hotkey_triggered)
            logger.info(f"Hotkey listener initialized for: {settings.hotkey_combination}")
            
            logger.info("All components initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize application: {e}")
            return False
    
    def _on_hotkey_triggered(self):
        """Callback function when hotkey is triggered."""
        logger.info("Hotkey triggered - starting screen capture and processing")

        # Schedule the processing in the main event loop from this thread
        if self.event_loop and not self.event_loop.is_closed():
            asyncio.run_coroutine_threadsafe(
                self._process_screen_capture(),
                self.event_loop
            )
        else:
            logger.error("Event loop not available for scheduling task")
    
    async def _process_screen_capture(self):
        """Process screen capture and run through the complete pipeline."""
        try:
            logger.info("Starting screen capture processing pipeline")
            
            # Step 1: Capture screen
            screenshot, file_path = self.screen_capture.capture_full_screen(save_to_disk=True)
            logger.info(f"Screen captured: {screenshot.size}")
            
            # Step 2: OCR processing
            logger.info("Starting OCR processing...")
            ocr_result = await self.ocr_pipeline.process_image(screenshot)
            
            if not ocr_result.success:
                logger.error(f"OCR processing failed: {ocr_result.error_message}")
                return
            
            logger.info("OCR processing completed successfully")
            
            # Save OCR result
            ocr_file = self._save_result("ocr", ocr_result.markdown_content)
            logger.info(f"OCR result saved to: {ocr_file}")
            
            # Step 3: Language analysis
            logger.info("Starting language analysis...")
            analysis_result = await self.analysis_pipeline.analyze_content(
                content=ocr_result.markdown_content,
                target_level="Intermediate",  # Could be configurable
                native_language="English"     # Could be configurable
            )
            
            if not analysis_result.success:
                logger.error(f"Analysis processing failed: {analysis_result.error_message}")
                return
            
            logger.info("Language analysis completed successfully")
            
            # Save analysis result
            analysis_file = self._save_result("analysis", analysis_result.analysis_content)
            logger.info(f"Analysis result saved to: {analysis_file}")
            
            # Display results summary
            self._display_results_summary(ocr_result, analysis_result, file_path, ocr_file, analysis_file)
            
        except Exception as e:
            logger.error(f"Error in processing pipeline: {e}")
    
    def _save_result(self, result_type: str, content: str) -> Path:
        """
        Save processing result to file.
        
        Args:
            result_type: Type of result (ocr, analysis)
            content: Content to save
            
        Returns:
            Path to saved file
        """
        from datetime import datetime
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{result_type}_{timestamp}.md"
        file_path = settings.temp_dir / filename
        
        file_path.write_text(content, encoding='utf-8')
        return file_path
    
    def _display_results_summary(self, ocr_result, analysis_result, screenshot_path, ocr_path, analysis_path):
        """Display a summary of processing results."""
        print("\n" + "="*60)
        print("LANGUAGE TUTOR - PROCESSING COMPLETE")
        print("="*60)
        print(f"Screenshot saved: {screenshot_path}")
        print(f"OCR result saved: {ocr_path}")
        print(f"Analysis result saved: {analysis_path}")
        print("\nOCR Statistics:")
        ocr_stats = self.ocr_pipeline.get_content_statistics(ocr_result.markdown_content)
        for key, value in ocr_stats.items():
            print(f"  {key}: {value}")
        
        print("\nAnalysis Statistics:")
        analysis_stats = self.analysis_pipeline.get_analysis_statistics(analysis_result.analysis_content)
        for key, value in analysis_stats.items():
            print(f"  {key}: {value}")
        
        print("\nPress Ctrl+PrintScreen again to capture another screen")
        print("Press Ctrl+C to exit")
        print("="*60)
    
    async def run(self):
        """Run the main application loop."""
        if not await self.initialize():
            logger.error("Failed to initialize application")
            return False

        # Store reference to the current event loop
        self.event_loop = asyncio.get_running_loop()

        logger.info("Starting Language Tutor application...")
        print("\n" + "="*60)
        print("LANGUAGE TUTOR - READY")
        print("="*60)
        print(f"Hotkey: {settings.hotkey_combination}")
        print("Press the hotkey to capture and analyze screen content")
        print("Press Ctrl+C to exit")
        print("="*60)

        self.is_running = True
        
        try:
            # Start hotkey listener in a separate thread
            import threading
            listener_thread = threading.Thread(target=self.hotkey_listener.start, daemon=True)
            listener_thread.start()
            
            # Keep the main loop running
            while self.is_running:
                await asyncio.sleep(0.1)
                
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        finally:
            await self.shutdown()
        
        return True
    
    async def shutdown(self):
        """Shutdown the application gracefully."""
        logger.info("Shutting down Language Tutor application...")
        self.is_running = False
        
        if self.hotkey_listener:
            self.hotkey_listener.stop()
        
        logger.info("Application shutdown complete")


def setup_signal_handlers(app: LanguageTutorApp):
    """Setup signal handlers for graceful shutdown."""
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}")
        app.is_running = False
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


async def main():
    """Main entry point for the application."""
    app = LanguageTutorApp()
    setup_signal_handlers(app)
    
    try:
        success = await app.run()
        return 0 if success else 1
    except Exception as e:
        logger.error(f"Unhandled exception in main: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
